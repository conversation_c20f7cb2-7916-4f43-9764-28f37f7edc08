import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { PropertyData } from 'src/app/models/property.model';
import { DeveloperDashboardModule } from '../developer-dashboard/developer-dashboard.module';
import { CommonModule } from '@angular/common';
import { ProjectsService } from '../services/projects.service';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-add-property',
  templateUrl: './add-property.component.html',
  styleUrl: './add-property.component.scss',
  standalone: true,
  imports: [DeveloperDashboardModule, CommonModule, ReactiveFormsModule],
})
export class AddPropertyComponent implements OnInit {
  totalSteps = 4;
  currentStep = 1;
  selectedCityId: any;
  selectedCityName: string;
  selectedAreaName: string;
  cities: any[] = [];
  areas: any[] = [];

  developerId: number;

  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private projectsService: ProjectsService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.developerId = JSON.parse(localStorage.getItem('currentUser')!).developerId;
    this.initForms();
    this.loadCities();
    this.loadAreas();
  }

  initForms() {
    // Step 1: Basic Property Settings
    this.step1Form = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ],
      ],
      designer: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      projectExecutor: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      managementTeam: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
    });

    // Step 2: Location Information
    this.step2Form = this.fb.group({
      cityId: ['', Validators.required],
      areaId: ['', Validators.required],
      type: ['', Validators.required],
      address: [
        '',
        [
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(200),
        ],
      ],
      googleMapUrl: ['', [Validators.pattern('https?://.+')]],
    });

    // Step 3: Project type
    this.step3Form = this.fb.group({
      projectType: ['', Validators.required],
      buildingsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      apartmentsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      villasCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      duplexCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      administrativeUnitsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      commercialUnitsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
    });

    // Step 4: Project Documents
    this.step4Form = this.fb.group({
      logoImage: [[], Validators.required],
      coverImage: [[], Validators.required],
      masterPlan: [[], Validators.required],
      gallery: [[]],
      videos: [[]],
    });
  }

  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      default:
        return this.step1Form;
    }
  }

  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  hasFieldError(fieldName: string): boolean {
    const form = this.getCurrentForm();
    const field = form.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.getCurrentForm().get(fieldName);

    if (!field?.errors) return '';

    if (field.errors['required']) return 'Required';
    if (field.errors['minlength']) return 'Too short';
    if (field.errors['maxlength']) return 'Too long';
    if (field.errors['min']) return 'Invalid number';
    if (field.errors['pattern']) return 'Invalid format';

    return 'Invalid';
  }

  areAllFormsValid(): boolean {
    return this.step1Form.valid && this.step2Form.valid && this.step3Form.valid;
  }

  nextStep() {
    if (!this.isCurrentFormValid()) {
      return;
    }

    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  selectProjectType(type: string) {
    this.step3Form.patchValue({
      projectType: type,
    });
  }

  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step2Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;
    this.step2Form.patchValue({
      areaId: areaId,
    });
  }

  submitForm() {
    if (this.areAllFormsValid()) {
      const formData: PropertyData = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value,
      };
      const httpFormData = new FormData();
      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      Object.keys(this.step2Form.value).forEach((key) => {
        httpFormData.append(key, this.step2Form.value[key]);
      });

      Object.keys(this.step3Form.value).forEach((key) => {
        httpFormData.append(key, this.step3Form.value[key]);
      });

      //add files
      const fileFields = [
        'logoImage',
        'coverImage',
        'masterPlan',
        'gallery',
        'videos',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        if (files && files.length) {
          const isMultiple = ['layout', 'videos'].includes(field);

          if (isMultiple) {
            files.forEach((file: File) => {
              httpFormData.append(`${field}[]`, file);
            });
          } else {
            httpFormData.append(field, files[0]);
          }
        }
      });

      httpFormData.append('developerId', this.developerId.toString());
      console.log('Property data submitted:', formData);

      this.projectsService.createProject(httpFormData).subscribe({
        next: async (response: any) => {
          await Swal.fire(
            'Project submitted successfully:',
            '',
            response.status
          );
          this.router.navigate(['/developer/projects']);
        },
        error: (err: any) => {
          Swal.fire(err.message, '', err.status);
        },
        complete: () => {
          this.cdr.detectChanges();
        },
      });
    }
  }

  submitFormWithOpenModels() {
    if (this.areAllFormsValid()) {
      const formData: PropertyData = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value,
      };
      const httpFormData = new FormData();
      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      Object.keys(this.step2Form.value).forEach((key) => {
        httpFormData.append(key, this.step2Form.value[key]);
      });

      Object.keys(this.step3Form.value).forEach((key) => {
        httpFormData.append(key, this.step3Form.value[key]);
      });

      //add files
      const fileFields = [
        'logoImage',
        'coverImage',
        'masterPlan',
        'gallery',
        'videos',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        if (files && files.length) {
          const isMultiple = ['layout', 'videos'].includes(field);

          if (isMultiple) {
            files.forEach((file: File) => {
              httpFormData.append(`${field}[]`, file);
            });
          } else {
            httpFormData.append(field, files[0]);
          }
        }
      });

      httpFormData.append('developerId', this.developerId.toString());
      console.log('Property data submitted:', formData);

      this.projectsService.createProject(httpFormData).subscribe({
        next: async (response: any) => {
          await Swal.fire(
            'Project submitted successfully:',
            '',
            response.status
          );
          this.router.navigate(['/developer/projects/models'], {
            queryParams: { projectId: response.data.id }
          });
        },
        error: (err: any) => {
          Swal.fire(err.message, '', err.status);
        },
        complete: () => {
          this.cdr.detectChanges();
        },
      });
    }
  }

  cancel() {
    this.router.navigate(['/developer/projects']);
  }

  onFileChange(event: any, fieldName: string) {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      this.step4Form.patchValue({
        [fieldName]: files,
      });

      console.log(`${fieldName}: ${files.length} files selected`);
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.step4Form.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  loadCities(): void {
    this.projectsService.getCities().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.projectsService.getAreas(cityId).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  // Helper methods for city and area names
  getCityName(city: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (city.name_ar || city.name_en) : city.name_en;
  }

  getAreaName(area: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (area.name_ar || area.name_en) : area.name_en;
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'PROJECT': 'مشروع',
        'TO_VIEW_EXISTING_PROJECTS': 'لعرض المشاريع الموجودة وتعديلها وإضافة مشاريع جديدة',
        'ADD_PROJECT': 'إضافة مشروع',
        'BASIC_DATA': 'البيانات الأساسية',
        'LOCATION': 'الموقع',
        'PROJECT_TYPE': 'نوع المشروع',
        'PROJECT_DOCUMENTS': 'مستندات المشروع',
        'STEP': 'الخطوة',
        'OF': 'من',
        'BACK_TO_PREVIOUS_STEP': 'العودة للخطوة السابقة',
        'PROJECT_NAME': 'اسم المشروع',
        'PROJECT_DESCRIPTION': 'وصف المشروع',
        'PROJECT_DESIGNER': 'مصمم المشروع',
        'PROJECT_IMPLEMENTER': 'منفذ المشروع',
        'PROJECT_MANAGEMENT': 'إدارة المشروع',
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'SELECT_CITY': 'اختر المدينة',
        'SELECT_AREA': 'اختر المنطقة',
        'DETAILED_ADDRESS': 'العنوان التفصيلي',
        'NO_CITIES_AVAILABLE': 'لا توجد مدن متاحة',
        'NO_AREAS_AVAILABLE': 'لا توجد مناطق متاحة',
        'ENTER_ADDRESS_DETAILS': 'أدخل العنوان بالتفصيل',
        'GOOGLE_MAPS_LINK': 'رابط الموقع على خرائط جوجل',
        'ENTER_MAP_LINK': 'أدخل رابط الخريطة',
        'RESIDENTIAL': 'سكني',
        'COMMERCIAL': 'تجاري',
        'ADMINISTRATIVE': 'إداري',
        'MIXED': 'مختلط',
        'SELECT_PROJECT_TYPE': 'اختر نوع المشروع',
        'NUMBER_OF_PROJECT_UNITS': 'عدد وحدات المشروع',
        'BUILDINGS': 'المباني',
        'APARTMENTS': 'الشقق',
        'VILLAS': 'الفيلات',
        'DUPLEX': 'الدوبلكس',
        'ADMINISTRATIVE_UNITS': 'الوحدات الإدارية',
        'COMMERCIAL_UNITS': 'الوحدات التجارية',
        'UPLOAD_DOCUMENTS': 'رفع المستندات',
        'PROJECT_LOGO': 'شعار المشروع',
        'PROJECT_BROCHURE': 'بروشور المشروع',
        'MASTER_PLAN': 'المخطط الرئيسي',
        'UPLOAD_PROJECT_LOGO': 'رفع شعار المشروع',
        'UPLOAD_PROJECT_COVER': 'رفع غلاف المشروع',
        'UPLOAD_PROJECT_MASTER_PLAN': 'رفع المخطط الرئيسي للمشروع',
        'UPLOAD_PROJECT_IMAGES': 'رفع صور المشروع',
        'UPLOAD_PROJECT_VIDEOS': 'رفع فيديوهات المشروع',
        'NEXT_LOCATION_INFO': 'التالي - معلومات الموقع',
        'NEXT_PROJECT_TYPE': 'التالي - نوع المشروع',
        'NEXT_PROJECT_DOCUMENTS': 'التالي - مستندات المشروع',
        'CREATE_NEW_PROJECT': 'إنشاء مشروع جديد',
        'CREATE_AND_UPLOAD_UNITS': 'إنشاء ورفع ملف الوحدات',
        'TYPE': 'النوع',
        'CHOOSE_TYPE': 'اختر النوع',
        'INSIDE_COMPOUND': 'داخل كمبوند',
        'OUTSIDE_COMPOUND': 'خارج كمبوند',
        'NEXT': 'التالي',
        'PREVIOUS': 'السابق',
        'SUBMIT': 'إرسال',
        'CANCEL': 'إلغاء',
        'SUCCESS': 'نجح!',
        'ERROR': 'خطأ!',
        'PROJECT_CREATED_SUCCESS': 'تم إنشاء المشروع بنجاح!',
        'PROJECT_CREATION_ERROR': 'حدث خطأ أثناء إنشاء المشروع. يرجى المحاولة مرة أخرى.',
        'PLEASE_FILL_REQUIRED_FIELDS': 'يرجى ملء جميع الحقول المطلوبة',
        'OK': 'موافق'
      },
      'en': {
        'PROJECT': 'project',
        'TO_VIEW_EXISTING_PROJECTS': 'To view existing projects, edit them, and add new projects',
        'ADD_PROJECT': 'Add Project',
        'BASIC_DATA': 'Basic Data',
        'LOCATION': 'Location',
        'PROJECT_TYPE': 'Project type',
        'PROJECT_DOCUMENTS': 'Project Documents',
        'STEP': 'Step',
        'OF': 'of',
        'BACK_TO_PREVIOUS_STEP': 'Back to previous step',
        'PROJECT_NAME': 'Project Name',
        'PROJECT_DESCRIPTION': 'Project Description',
        'PROJECT_DESIGNER': 'Project Designer',
        'PROJECT_IMPLEMENTER': 'Project implementer',
        'PROJECT_MANAGEMENT': 'Project management',
        'CITY': 'City',
        'AREA': 'Area',
        'SELECT_CITY': 'Select City',
        'SELECT_AREA': 'Select Area',
        'DETAILED_ADDRESS': 'Detailed Address',
        'NO_CITIES_AVAILABLE': 'No cities available',
        'NO_AREAS_AVAILABLE': 'No areas available',
        'ENTER_ADDRESS_DETAILS': 'Enter the address in details',
        'GOOGLE_MAPS_LINK': 'Website link on Google Maps',
        'ENTER_MAP_LINK': 'Enter the map link',
        'RESIDENTIAL': 'Residential',
        'COMMERCIAL': 'Commercial',
        'ADMINISTRATIVE': 'Administrative',
        'MIXED': 'Mixed',
        'SELECT_PROJECT_TYPE': 'commercial...',
        'NUMBER_OF_PROJECT_UNITS': 'Number of project units',
        'BUILDINGS': 'Buildings',
        'APARTMENTS': 'Apartments',
        'VILLAS': 'Villas',
        'DUPLEX': 'Duplex',
        'ADMINISTRATIVE_UNITS': 'Administrative Units',
        'COMMERCIAL_UNITS': 'Commercial Units',
        'UPLOAD_DOCUMENTS': 'Upload Documents',
        'PROJECT_LOGO': 'Project Logo',
        'PROJECT_BROCHURE': 'Project Brochure',
        'MASTER_PLAN': 'Master Plan',
        'UPLOAD_PROJECT_LOGO': 'upload project logo',
        'UPLOAD_PROJECT_COVER': 'upload project Cover',
        'UPLOAD_PROJECT_MASTER_PLAN': 'upload project Master Plan',
        'UPLOAD_PROJECT_IMAGES': 'upload project images',
        'UPLOAD_PROJECT_VIDEOS': 'upload project videos',
        'NEXT_LOCATION_INFO': 'Next - Location Information',
        'NEXT_PROJECT_TYPE': 'Next - Project type',
        'NEXT_PROJECT_DOCUMENTS': 'Next - Project Documents',
        'CREATE_NEW_PROJECT': 'Create a new project',
        'CREATE_AND_UPLOAD_UNITS': 'Create and upload units file',
        'TYPE': 'Type',
        'CHOOSE_TYPE': 'Choose type',
        'INSIDE_COMPOUND': 'Inside Compound',
        'OUTSIDE_COMPOUND': 'Outside Compound',
        'NEXT': 'Next',
        'PREVIOUS': 'Previous',
        'SUBMIT': 'Submit',
        'CANCEL': 'Cancel',
        'SUCCESS': 'Success!',
        'ERROR': 'Error!',
        'PROJECT_CREATED_SUCCESS': 'Project created successfully!',
        'PROJECT_CREATION_ERROR': 'An error occurred while creating the project. Please try again.',
        'PLEASE_FILL_REQUIRED_FIELDS': 'Please fill all required fields',
        'OK': 'OK'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
