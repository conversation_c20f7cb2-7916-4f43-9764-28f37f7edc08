.card {
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e6ef;

  .card-body {
    padding: 1.5rem;
  }

  &.cursor-pointer {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }
}

.text-dark-blue {
  color: #0d6efd; /* More vibrant blue color */
}

// Upload card styling
.upload-card-container {
  .card {
    transition: all 0.2s ease;
    border-radius: 25px;
    border: 1px solid #e4e6ef;

    label {
      cursor: pointer;
      font-size: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 0;

      .upload-icon {
        width: 32px;
        height: 32px;
        background-color: #0d6efd;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }

      .upload-text {
        color: #0d6efd;
        font-weight: bold;
      }
    }

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);
    }
  }
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

.cities-dropdown {
  max-height: 300px;
  overflow-y: auto;
}

.cities-list {
  max-height: 200px;
  overflow-y: auto;

  li {
    margin: 0;
    padding: 0;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Custom scrollbar styling
.cities-list::-webkit-scrollbar {
  width: 6px;
}

.cities-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cities-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;

  &:hover {
    background: #555;
  }
}

// Ensure the dropdown menu has proper padding
.dropdown-menu {
  padding: 0.5rem 0;
  margin: 0;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.areas-dropdown {
  max-height: 300px;
  overflow-y: auto;
}

.areas-list {
  max-height: 200px;
  overflow-y: auto;

  li {
    margin: 0;
    padding: 0;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Custom scrollbar styling for areas
.areas-list::-webkit-scrollbar {
  width: 6px;
}

.areas-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.areas-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;

  &:hover {
    background: #555;
  }
}

// Center the back button for all languages
.text-primary.cursor-pointer.mb-2.text-center {
  text-align: center !important;
  display: block !important;
  width: 100% !important;
  margin: 0 auto !important;
}

// Global CSS to center step progress text
.card .card-body .d-flex.flex-column .d-flex.align-items-center.mb-2 {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  width: 100% !important;

  .text-primary {
    text-align: center !important;
    margin: 0 auto !important;
    display: block !important;
  }
}

// Step indicator styling
.step-indicator-text {
  font-size: 1rem;
  font-weight: 600;
}

.step-separator {
  font-size: 0.9rem;
  margin: 0 0.5rem;
}

.step-total {
  font-size: 1rem;
  font-weight: 500;
}

// Center progress text in Arabic
:host-context(html[lang="ar"]) {
  .d-flex.align-items-center.mb-2 {
    justify-content: center !important;
    text-align: center !important;

    .text-primary {
      margin: 0 auto !important;
      text-align: center !important;
    }
  }

  // Alternative approach for progress text
  .card-body .d-flex.flex-column .d-flex.align-items-center.mb-2 {
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    direction: ltr !important;
    margin: 0 auto !important;
    width: 100% !important;
  }

  // More specific targeting for step progress
  .card .card-body .d-flex.flex-column {
    .d-flex.align-items-center.mb-2 {
      justify-content: center !important;
      align-items: center !important;
      text-align: center !important;

      .text-primary {
        text-align: center !important;
        margin: 0 auto !important;
        display: block !important;
        width: auto !important;
      }
    }
  }

  // Force center alignment for step indicator
  .text-primary.fw-bold {
    text-align: center !important;
    justify-self: center !important;
    align-self: center !important;
  }
}

// RTL Support for Add Property Form
:host-context(html[lang="ar"]) {
  .text-start {
    text-align: right !important;
  }

  .form-label {
    text-align: right;
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .form-control {
    text-align: right;
    direction: rtl;

    &::placeholder {
      text-align: right;
      direction: rtl;
    }
  }

  select.form-control {
    text-align: right;
    direction: rtl;

    option {
      text-align: right;
      direction: rtl;
    }
  }

  .dropdown-toggle {
    text-align: right;
    direction: rtl;

    .fas {
      margin-left: 0;
      margin-right: auto;
      transform: scaleX(-1); // Flip the chevron icon horizontally
    }
  }

  // Reverse dropdown menu position for Arabic
  .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    text-align: right;
    direction: rtl;

    // Position dropdown to the right side
    &[data-bs-popper] {
      right: 0 !important;
      left: auto !important;
    }
  }

  .dropdown-item {
    text-align: right;
    direction: rtl;
  }

  .text-dark-blue {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .invalid-feedback {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Noto Kufi Arabic', sans-serif;
    font-weight: 600;
  }

  .progress {
    direction: ltr; // Keep progress bar LTR
  }

  .d-flex {
    &.justify-content-between {
      flex-direction: row-reverse;
    }
  }

  .upload-text {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  .cursor-pointer {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  // Step indicator styling for Arabic
  .step-indicator-text {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
  }

  .step-separator {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1rem !important;
    margin: 0 0.75rem !important;
  }

  .step-total {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
  }

  // Reverse button order for Arabic
  .d-flex.flex-column.flex-sm-row.justify-content-between {
    flex-direction: row-reverse !important;

    @media (max-width: 575.98px) {
      flex-direction: column !important;

      .order-1 {
        order: 2 !important;
      }

      .order-2 {
        order: 1 !important;
      }
    }
  }

  // Additional dropdown styling for better RTL support
  .dropdown {
    .dropdown-toggle {
      justify-content: space-between;

      span {
        text-align: right;
        flex-grow: 1;
      }

      .fas {
        order: -1; // Move icon to the left in RTL
        margin-right: 8px;
        margin-left: 0;
      }
    }

    .dropdown-menu {
      min-width: 100%;

      .dropdown-item {
        text-align: right;
        padding-right: 1rem;
        padding-left: 0.5rem;

        &:hover,
        &:focus {
          background-color: #f8f9fa;
        }
      }
    }
  }

  // Fix for Bootstrap dropdown positioning in RTL
  .dropdown-menu[data-bs-popper] {
    right: 0 !important;
    left: auto !important;
    transform: translate3d(0px, 38px, 0px) !important;
  }

  // Ensure all dropdowns in the form are RTL
  #compoundTypeDropdown + .dropdown-menu,
  #propertyTypeDropdown + .dropdown-menu,
  #unitTypeDropdownStep0 + .dropdown-menu,
  #cityDropdownStep1 + .dropdown-menu,
  #areaDropdownStep1 + .dropdown-menu,
  #subAreaDropdown + .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    text-align: right;
    direction: rtl;
  }

  // Specific styling for Step 1 dropdowns
  .areas-dropdown {
    right: 0 !important;
    left: auto !important;
    text-align: right;
    direction: rtl;

    .areas-list {
      .dropdown-item {
        text-align: right;
        padding-right: 1rem;
        padding-left: 0.5rem;
      }
    }
  }

  // Style for all dropdown buttons
  .btn.dropdown-toggle {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;

    .fas.fa-chevron-down {
      order: -1;
      margin-right: 8px;
      margin-left: 0;
    }
  }
}
