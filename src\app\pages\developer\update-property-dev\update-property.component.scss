.card {
  max-width: 550px;
  margin: 0 auto;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e4e6ef;

  .card-body {
    padding: 1.5rem;
  }

  &.cursor-pointer {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }
}

.text-dark-blue {
  color: #0d6efd; /* More vibrant blue color */
}

// Step indicator styling
.step-indicator-text {
  font-size: 1rem;
  font-weight: 600;
}

.step-separator {
  font-size: 0.9rem;
  margin: 0 0.5rem;
}

.step-total {
  font-size: 1rem;
  font-weight: 500;
}

// Force center alignment for step indicator with high specificity
.card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2 {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
}

// Additional specificity for RTL layout
html[dir="rtl"] .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2,
html[lang="ar"] .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2,
.rtl-layout .card .card-body .stepper .d-flex.justify-content-center.align-items-center.mb-2 {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
}

// Maximum specificity override for step indicator
html[dir="rtl"] .card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2,
html[lang="ar"] .card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2,
.card .card-body .d-flex.flex-column .d-flex.justify-content-center.align-items-center.mb-2.rtl-layout {
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  direction: ltr !important;
  display: flex !important;
  flex-direction: row !important;
  width: 100% !important;
}

// Upload card styling
.upload-card-container {
  .card {
    transition: all 0.2s ease;
    border-radius: 25px;
    border: 1px solid #e4e6ef;

    label {
      cursor: pointer;
      font-size: 1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 0;

      .upload-icon {
        width: 32px;
        height: 32px;
        background-color: #0d6efd;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }

      .upload-text {
        color: #0d6efd;
        font-weight: bold;
      }
    }

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 0 10px rgba(13, 110, 253, 0.1);
    }
  }
}

.btn-dark-blue {
  background-color: #1e1e2d;
  color: #ffffff;
}

.btn-navy {
  background-color: #1e1e7c;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #16165a;
  }
  &:disabled {
    background-color: #9999c9;
  }
}

.btn-blue-custom {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  &:hover {
    background-color: #0056b3;
  }
  &:disabled {
    background-color: #6c757d;
    color: #ffffff;
  }
}

.btn-green-custom {
  background-color: #ffffff;
  color: #000000;
  border: 2px solid #28a745;
  &:hover {
    background-color: #28a745;
    color: #ffffff;
    border-color: #28a745;
  }
  &:focus {
    background-color: #ffffff;
    color: #000000;
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}

.progress {
  border-radius: 30px;
}

.progress-bar {
  border-radius: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

// Custom styling for the form
.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

.form-select {
  border-radius: 8px;
  padding: 0.75rem 1rem;
}

// Dropdown styling
.dropdown {
  .btn-outline-secondary {
    border-radius: 8px;
    border: 1px solid #e4e6ef;
    background-color: #f5f8fa;
    color: #5e6278;
    padding: 0.75rem 1rem;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      border-color: #e4e6ef;
    }

    &::after {
      display: none;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
    padding: 0.5rem 0;

    .dropdown-item {
      padding: 0.75rem 1.25rem;
      cursor: pointer;

      &:hover {
        background-color: #f5f8fa;
      }
    }
  }
}

// Existing files display styling
.existing-files-container {
  background-color: #f8f9fa;
  border-top: 1px solid #e4e6ef;

  h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #5e6278;
    margin-bottom: 0.75rem;
  }

  .file-item {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);

      .delete-btn {
        opacity: 1;
      }
    }

    img {
      border: 2px solid #e4e6ef;
      transition: all 0.2s ease;

      &:hover {
        border-color: #0d6efd;
      }
    }

    .delete-btn {
      opacity: 0;
      transition: all 0.2s ease;
      width: 5px;
      height: 5px;
      padding: 0;

      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        transform: scale(1.1);
      }
    }

    .video-overlay-small {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      pointer-events: none;
    }
  }
}

// Badge styling for file counts
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;

  &.bg-info {
    background-color: #0dcaf0 !important;
  }

  &.bg-success {
    background-color: #198754 !important;
  }
}

// RTL Support for Update Property
.rtl-layout {
  direction: rtl;
  text-align: right;

  .card-body {
    direction: rtl;
    text-align: right;
  }

  .form-control,
  .form-select {
    direction: rtl;
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }

  .stepper {
    direction: rtl;
  }

  .progress {
    direction: ltr; // Keep progress bar LTR for consistency
  }
}

.rtl-alert {
  direction: rtl;
  text-align: center;

  strong {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}

.rtl-header {
  direction: rtl;
  text-align: center;
  font-family: 'Hacen Liner Screen St', sans-serif;
}

// Override global RTL text alignment issues
html[dir="rtl"] .update-property-form .text-end,
html[lang="ar"] .update-property-form .text-end {
  text-align: right !important;
}

html[dir="rtl"] .update-property-form .text-start,
html[lang="ar"] .update-property-form .text-start {
  text-align: right !important;
}

// Force correct text alignment for form elements in Arabic
.update-property-form {
  .form-label {
    text-align: right !important;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    text-align: right !important;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .dropdown-toggle {
    text-align: right !important;
    justify-content: space-between !important;
  }

  .dropdown-item {
    text-align: right !important;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .upload-text {
    font-family: 'Noto Kufi Arabic', sans-serif;
    text-align: center !important;
  }

  .btn {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  h2, h3, h4, h5 {
    font-family: 'Noto Kufi Arabic', sans-serif;
    text-align: center !important;
  }

  .text-muted {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right !important;
  }
}

// Specific override for Arabic language in update form
html[lang="ar"] .update-property-form,
html[dir="rtl"] .update-property-form {
  direction: rtl !important;

  * {
    text-align: right !important;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn, .upload-text, h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
  }

  .text-center, .upload-text {
    text-align: center !important;
  }

  .step-indicator-text, .step-separator, .step-total {
    text-align: center !important;
  }
}

// RTL Support for Arabic
.rtl-layout {
  direction: rtl;
  text-align: right;

  // Step indicator styling for Arabic with reversed order
  .d-flex.justify-content-center.align-items-center.mb-2 {
    flex-direction: row-reverse !important;
  }

  .step-indicator-text {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    order: 3 !important;
  }

  .step-separator {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1rem !important;
    margin: 0 0.75rem !important;
    order: 2 !important;
  }

  .step-total {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 500 !important;
    order: 1 !important;
  }

  // Project units section RTL support
  .d-flex.mb-3.align-items-center {
    flex-direction: row-reverse;

    .btn {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    .flex-grow-1 {
      .form-control {
        text-align: right !important;
      }
    }
  }

  .form-control {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }

  .btn {
    font-family: 'Noto Kufi Arabic', sans-serif;
  }

  h2, h3, h4, h5 {
    font-family: 'Noto Kufi Arabic', sans-serif;
    font-weight: bold;
  }

  // Dropdown positioning and RTL support
  .dropdown-toggle {
    text-align: right !important;
    direction: rtl !important;
    justify-content: space-between !important;

    .fas {
      margin-left: 0;
      margin-right: auto;
      transform: scaleX(-1);
      order: -1;
    }

    span {
      text-align: right;
      flex-grow: 1;
    }
  }

  .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    text-align: right !important;
    direction: rtl !important;

    &[data-bs-popper] {
      right: 0 !important;
      left: auto !important;
      transform: translate3d(0px, 38px, 0px) !important;
    }
  }

  .dropdown-item {
    text-align: right !important;
    direction: rtl !important;
    font-family: 'Hacen Liner Screen St', sans-serif !important;
    padding-right: 1rem !important;
    padding-left: 0.5rem !important;

    &:hover,
    &:focus {
      background-color: #f5f8fa;
      text-align: right !important;
    }
  }
}

:host-context(html[lang="ar"]) {
  .card {
    direction: rtl;

    .card-body {
      direction: rtl;
      text-align: right;
    }
  }

  .form-control,
  .form-select {
    direction: rtl;
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  label {
    font-family: 'Hacen Liner Screen St', sans-serif;
    text-align: right;
  }

  h2, h3, h4, h5, h6 {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .alert {
    direction: rtl;
    text-align: center;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .stepper {
    direction: rtl;
  }

  .upload-card-container {
    .card label {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }
}

// Additional RTL classes
.rtl-form {
  direction: rtl;
  text-align: right;
}

.rtl-buttons {
  direction: rtl;

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}
