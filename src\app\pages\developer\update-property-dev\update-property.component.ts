import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { DeveloperDashboardModule } from '../developer-dashboard/developer-dashboard.module';
import { CommonModule } from '@angular/common';
import { ProjectsService } from '../services/projects.service';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-update-property',
  templateUrl: './update-property.component.html',
  styleUrl: './update-property.component.scss',
  standalone: true,
  imports: [DeveloperDashboardModule, CommonModule, ReactiveFormsModule],
})
export class UpdatePropertyComponent implements OnInit {
  totalSteps = 4;
  currentStep = 1;
  projectId: number | null = null;
  existingFiles: any = {};

  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;

  cities: any[] = [];
  areas: any[] = [];
  developerId: any;
  selectedCityId: number | null = null;
  selectedCityName: string = '';
  selectedAreaName: string = '';

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private projectsService: ProjectsService,
    private cdr: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.initForms();
    this.loadCities();

    const userJson = localStorage.getItem('currentUser');
    let user = userJson ? JSON.parse(userJson) : null;
    this.developerId = user?.developerId;

    this.route.queryParams.subscribe((params) => {
      if (params['id']) {
        this.projectId = +params['id'];
        this.loadProjectData();
      } else {
        this.router.navigate(['/developer/projects']);
      }
    });
  }

  initForms() {
    // Step 1: Basic Property Settings
    this.step1Form = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ],
      ],
      designer: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      projectExecutor: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      managementTeam: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
    });

    // Step 2: Location Information
    this.step2Form = this.fb.group({
      cityId: ['', Validators.required],
      areaId: ['', Validators.required],
      address: [
        '',
        [
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(200),
        ],
      ],
      googleMapsLink: ['', [Validators.pattern('https?://.+')]],
      googleMapUrl: ['', [Validators.pattern('https?://.+')]],
    });

    // Step 3: Project type
    this.step3Form = this.fb.group({
      projectType: ['', Validators.required],
      buildingsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      apartmentsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      villasCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      duplexCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      administrativeUnitsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
      commercialUnitsCount: [
        '',
        [
          Validators.required,
          Validators.min(0),
          Validators.pattern('^[0-9]+$'),
        ],
      ],
    });

    // Step 4: Project Documents
    this.step4Form = this.fb.group({
      logoImage: [[]],
      coverImage: [[]],
      masterPlan: [[]],
      gallery: [[]],
      videos: [[]],
    });
  }

  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      default:
        return this.step1Form;
    }
  }

  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  hasFieldError(fieldName: string): boolean {
    const form = this.getCurrentForm();
    const field = form.get(fieldName);
    return field ? field.invalid && (field.dirty || field.touched) : false;
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.getCurrentForm().get(fieldName);

    if (!field?.errors) return '';

    if (field.errors['required']) return 'Required';
    if (field.errors['minlength']) return 'Too short';
    if (field.errors['maxlength']) return 'Too long';
    if (field.errors['min']) return 'Invalid number';
    if (field.errors['pattern']) return 'Invalid format';

    return 'Invalid';
  }

  markCurrentFormAsTouched(): void {
    const form = this.getCurrentForm();
    Object.keys(form.controls).forEach((key) => {
      form.get(key)?.markAsTouched();
    });
  }

  areAllFormsValid(): boolean {
    return this.step1Form.valid && this.step2Form.valid && this.step3Form.valid;
  }

  nextStep() {
    this.markCurrentFormAsTouched();

    if (!this.isCurrentFormValid()) {
      return;
    }

    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  loadCities(): void {
    this.projectsService.getCities().subscribe({
      next: (response: any) => {
        console.log('Cities response:', response);

        if (response && response.data && Array.isArray(response.data)) {
          this.cities = response.data;
        } else if (response && Array.isArray(response)) {
          this.cities = response;
        } else if (response && response.cities) {
          this.cities = response.cities;
        } else {
          console.warn('No cities data in response:', response);
          this.cities = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.projectsService.getAreas(cityId).subscribe({
      next: (response: any) => {
        if (response && response.data && Array.isArray(response.data)) {
          this.areas = response.data;
        } else if (response && Array.isArray(response)) {
          this.areas = response;
        } else if (response && response.areas) {
          this.areas = response.areas;
        } else {
          this.areas = [];
        }
      },
      error: (err: any) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step2Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaName = areaName;
    this.step2Form.patchValue({
      areaId: areaId,
    });
  }

  selectProjectType(type: string) {
    this.step3Form.patchValue({
      projectType: type,
    });
  }

  submitForm() {
    this.markCurrentFormAsTouched();

    if (this.areAllFormsValid()) {
      const httpFormData = new FormData();

      Object.keys(this.step1Form.value).forEach((key) => {
        httpFormData.append(key, this.step1Form.value[key]);
      });

      Object.keys(this.step2Form.value).forEach((key) => {
        if (
          this.step2Form.value[key] !== null &&
          this.step2Form.value[key] !== ''
        ) {
          httpFormData.append(key, this.step2Form.value[key]);
        }
      });

      Object.keys(this.step3Form.value).forEach((key) => {
        httpFormData.append(key, this.step3Form.value[key]);
      });

      const fileFields = [
        'logoImage',
        'coverImage',
        'masterPlan',
        'gallery',
        'videos',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        const isMultiple = ['gallery', 'videos'].includes(field);

        if (isMultiple && Array.isArray(files) && files.length > 0) {
          files.forEach((file: File) => {
            if (file instanceof File) {
              httpFormData.append(`${field}[]`, file);
            }
          });
        } else if (!isMultiple && files[0] instanceof File) {
          httpFormData.append(field, files[0]);
        }
      });

      httpFormData.append('developerId', this.developerId );

      if (this.projectId !== null) {
        this.projectsService
          .updateProject(this.projectId, httpFormData)
          .subscribe({
            next: (response) => {
              console.log('Project updated successfully:', response);
              this.router.navigate(['/developer/projects']);
            },
            error: (error) => {
              console.error('Error updating project:', error);
            },
          });
      } else {
        console.error('Project ID is null');
      }
    }
  }

  cancel() {
    this.router.navigate(['/developer/projects']);
  }

  onFileChange(event: any, fieldName: string) {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      this.step4Form.patchValue({
        [fieldName]: files,
      });

      console.log(`${fieldName}: ${files.length} files selected`);
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.step4Form.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  loadProjectData() {
    if (this.projectId) {
      this.projectsService.getById(this.projectId).subscribe({
        next: (response) => {
          const data = response.data || response;
          console.log('Project data:', data);

          // Simple direct assignment
          this.existingFiles = {
            logoImage: data.logoImage || null,
            coverImage: data.coverImage || null,
            masterPlan: data.masterPlan || null,
            galleryImages:
              data.gallery?.filter((item: any) => item.type === 'image') || [],
            galleryVideos:
              data.gallery?.filter((item: any) => item.type === 'video') || [],
          };

          setTimeout(() => {
            this.step1Form.patchValue({
              name: data.name || '',
              designer: data.designer || '',
              projectExecutor: data.projectExecutor || '',
              managementTeam: data.managementTeam || '',
            });

            // Populate Step 2 form with existing data
            this.step2Form.patchValue({
              cityId: data.city?.id || data.cityId || '',
              areaId: data.area?.id || data.areaId || '',
              address: data.address || '',
              googleMapsLink: data.googleMapUrl || '',
              googleMapUrl: data.googleMapUrl || '',
            });

            // Set selected values for display
            if (data.city) {
              this.selectedCityId = data.city.id || data.cityId;
              this.selectedCityName = data.city.name_en || data.city.name || '';
              if (this.selectedCityId) {
                this.loadAreas(this.selectedCityId);
              }
            }
            if (data.area) {
              this.selectedAreaName = data.area.name_en || data.area.name || '';
            }

            // Populate Step 3 form with existing data
            this.step3Form.patchValue({
              projectType: data.projectType || '',
              buildingsCount: data.buildingsCount || 0,
              apartmentsCount: data.apartmentsCount || 0,
              villasCount: data.villasCount || 0,
              duplexCount: data.duplexCount || 0,
              administrativeUnitsCount: data.administrativeUnitsCount || 0,
              commercialUnitsCount: data.commercialUnitsCount || 0,
            });

            this.cdr.detectChanges();
          }, 100);
        },
        error: (error) => {
          console.error('Error loading project data:', error);
          alert('Failed to load project data. Please try again later.');
        },
      });
    }
  }

  removeImage(fieldName: string): void {
    this.existingFiles[fieldName] = null;

    this.cdr.detectChanges();
  }

  removeGalleryItem(type: string, index: number): void {
    if (type === 'image') {
      this.existingFiles.galleryImages.splice(index, 1);
    } else if (type === 'video') {
      this.existingFiles.galleryVideos.splice(index, 1);
    }

    this.cdr.detectChanges();
  }

  // Helper methods for city and area names
  getCityName(city: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (city.name_ar || city.name_en || city.name) : (city.name_en || city.name);
  }

  getAreaName(area: any): string {
    const currentLang = this.translationService.getCurrentLanguage();
    return currentLang === 'ar' ? (area.name_ar || area.name_en || area.name) : (area.name_en || area.name);
  }

  getProjectTypeDisplayName(value: string): string {
    if (!value) return '';
    const trimmedValue = value.trim();
    switch (trimmedValue.toLowerCase()) {
      case 'residential':
        return this.getTranslatedText('RESIDENTIAL');
      case 'commercial':
        return this.getTranslatedText('COMMERCIAL');
      case 'mixed':
        return this.getTranslatedText('MIXED');
      default:
        return trimmedValue;
    }
  }

  // Helper method to get translated text
  getTranslatedText(key: string): string {
    const translations: any = {
      'ar': {
        'UPDATE_PROJECT': 'تحديث المشروع',
        'EDIT_AND_UPDATE_EXISTING_PROJECT': 'تعديل وتحديث معلومات المشروع الحالي',
        'EDIT_MODE': 'وضع التعديل',
        'YOU_ARE_IN_EDIT_MODE': 'أنت في وضع التعديل. أي تغييرات ستحفظ في المشروع الحالي.',
        'BASIC_DATA': 'البيانات الأساسية',
        'LOCATION': 'الموقع',
        'PROJECT_TYPE': 'نوع المشروع',
        'PROJECT_DOCUMENTS': 'مستندات المشروع',
        'NUMBER_OF_PROJECT_UNITS': 'عدد وحدات المشروع',
        'BUILDINGS': 'المباني',
        'APARTMENTS': 'الشقق',
        'VILLAS': 'الفيلات',
        'DUPLEX': 'الدوبلكس',
        'ADMINISTRATIVE_UNITS': 'الوحدات الإدارية',
        'COMMERCIAL_UNITS': 'الوحدات التجارية',
        'RESIDENTIAL': 'سكني',
        'COMMERCIAL': 'تجاري',
        'MIXED': 'مختلط',
        'CHOOSE_PROJECT_TYPE': 'اختر نوع المشروع',
        'STEP': 'الخطوة',
        'OF': 'من',
        'PROJECT_NAME': 'اسم المشروع',
        'DESIGNER': 'المصمم',
        'PROJECT_EXECUTOR': 'منفذ المشروع',
        'DESCRIPTION': 'الوصف',
        'DELIVERY_DATE': 'تاريخ التسليم',
        'DELIVERY_STATUS': 'حالة التسليم',
        'CITY': 'المدينة',
        'AREA': 'المنطقة',
        'COMPOUND_TYPE': 'نوع المجمع',
        'NEXT': 'التالي',
        'PREVIOUS': 'السابق',
        'UPDATE': 'تحديث',
        'CANCEL': 'إلغاء',
        'REQUIRED_FIELD': 'هذا الحقل مطلوب',
        'MIN_LENGTH': 'الحد الأدنى للطول',
        'MAX_LENGTH': 'الحد الأقصى للطول',
        'SELECT_CITY': 'اختر المدينة',
        'SELECT_AREA': 'اختر المنطقة',
        'SELECT_COMPOUND_TYPE': 'اختر نوع المجمع',
        'SELECT_DELIVERY_STATUS': 'اختر حالة التسليم',
        'UNDER_CONSTRUCTION': 'تحت الإنشاء',
        'READY': 'جاهز',
        'DELIVERED': 'مسلم',
        'UPLOAD_IMAGES': 'رفع الصور',
        'UPLOAD_VIDEOS': 'رفع الفيديوهات',
        'UPLOAD_DOCUMENTS': 'رفع المستندات',
        'REMOVE': 'إزالة'
      },
      'en': {
        'UPDATE_PROJECT': 'Update Project',
        'EDIT_AND_UPDATE_EXISTING_PROJECT': 'Edit and update existing project information',
        'EDIT_MODE': 'Edit mode',
        'YOU_ARE_IN_EDIT_MODE': 'You are in edit mode. Any changes you make will be saved to the existing project.',
        'BASIC_DATA': 'Basic Data',
        'LOCATION': 'Location',
        'PROJECT_TYPE': 'Project type',
        'PROJECT_DOCUMENTS': 'Project Documents',
        'NUMBER_OF_PROJECT_UNITS': 'Number of project units',
        'BUILDINGS': 'Buildings',
        'APARTMENTS': 'Apartments',
        'VILLAS': 'Villas',
        'DUPLEX': 'Duplex',
        'ADMINISTRATIVE_UNITS': 'Administrative Units',
        'COMMERCIAL_UNITS': 'Commercial Units',
        'RESIDENTIAL': 'Residential',
        'COMMERCIAL': 'Commercial',
        'MIXED': 'Mixed',
        'CHOOSE_PROJECT_TYPE': 'Choose project type',
        'PROJECT_NAME': 'Project Name',
        'DESIGNER': 'Designer',
        'PROJECT_EXECUTOR': 'Project Executor',
        'DESCRIPTION': 'Description',
        'DELIVERY_DATE': 'Delivery Date',
        'DELIVERY_STATUS': 'Delivery Status',
        'CITY': 'City',
        'AREA': 'Area',
        'COMPOUND_TYPE': 'Compound Type',
        'NEXT': 'Next',
        'PREVIOUS': 'Previous',
        'UPDATE': 'Update',
        'CANCEL': 'Cancel',
        'REQUIRED_FIELD': 'This field is required',
        'MIN_LENGTH': 'Minimum length',
        'MAX_LENGTH': 'Maximum length',
        'SELECT_CITY': 'Select City',
        'SELECT_AREA': 'Select Area',
        'SELECT_COMPOUND_TYPE': 'Select Compound Type',
        'SELECT_DELIVERY_STATUS': 'Select Delivery Status',
        'UNDER_CONSTRUCTION': 'Under Construction',
        'READY': 'Ready',
        'DELIVERED': 'Delivered',
        'UPLOAD_IMAGES': 'Upload Images',
        'UPLOAD_VIDEOS': 'Upload Videos',
        'UPLOAD_DOCUMENTS': 'Upload Documents',
        'REMOVE': 'Remove',
        'STEP': 'Step',
        'OF': 'of'
      }
    };

    const currentLang = this.translationService.getCurrentLanguage();
    return translations[currentLang]?.[key] || key;
  }
}
